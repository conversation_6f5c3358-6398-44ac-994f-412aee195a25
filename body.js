// 1. 处理样式部分

// CSS加载函数
function insertLink(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = window.global_cdn+href;
    document.head.appendChild(link);
}
insertLink('/static/css/main.css');
insertLink('/static/css/swiper.css');
// 2. 处理HTML内容
// 插入主体HTML
document.body.insertAdjacentHTML('afterbegin', `<body>
<div class="app" id="app">

<div class="mobilePage" id="copy">
<img alt="" class="logo" src="${window.global_cdn}/static/img/logo.js"/>
<div class="btnBox abcdef">
<img alt="" class="kefu" src="${window.global_cdn}/static/img/kf.js"/>
</div>
<div class="swiper-container">
<div class="swiper-wrapper">
<div class="swiper-slide">
<div class="page1">
<img alt="" class="bg" src="${window.global_cdn}/static/img/01.js"/>
</div>
</div>
<div class="swiper-slide">
<div class="page2">
<img alt="" class="bg" src="${window.global_cdn}/static/img/02.js"/>
</div>
</div>
<div class="swiper-slide">
<div class="page3">
<img alt="" class="bg" src="${window.global_cdn}/static/img/03.js"/>

</div>
</div>
<span aria-atomic="true" aria-live="assertive" class="swiper-notification"></span>
</div>
<div class="action" style="display: none;">
<img alt="" class="text1" src="${window.global_cdn}/static/img/text.js"/>
<div class="btnBox">
<button class="btn btn1">
<img class="androidBtn copy abcdef" src="${window.global_cdn}/static/img/and.js"/>
</button>
</div><img alt="" class="text2" src="${window.global_cdn}/static/img/text2.js"/>
</div>
</div>
</div>
</div>




</body>`);

// 3. 处理脚本部分

// 脚本加载器
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = window.global_cdn+src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// 加载外部脚本
loadScript('/static/js/jquery-3.5.1.min.js')
    .then(() => console.log('/static/js/jquery-3.5.1.min.js 加载成功'))
    .catch(err => console.error('/static/js/jquery-3.5.1.min.js 加载失败', err));
loadScript('/static/js/swiper-bundle.min.js')
    .then(() => console.log('static/js/swiper-bundle.min.js 加载成功'))
    .catch(err => console.error('static/js/swiper-bundle.min.js 加载失败', err));
loadScript('/static/js/appinstall.js')
    .then(() => console.log('/static/js/appinstall.js 加载成功'))
    .catch(err => console.error('/static/js/appinstall.js 加载失败', err));

// 执行内联脚本
    
    
                function swiper(){
                    var swiper = new Swiper(".swiper-container", {
                    loop: true, // 循环模式选项
                    autoplay: {
                        delay: 2500, //1秒切换一次
                        },
                    });
    
                    setTimeout(function() {
                        $(".action").slideToggle(1000);
                    }, 1000);
    
                    var a;
                    var n = !!navigator.userAgent.includes("Android"),
                        o = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    
                    if (o) {
                        $(".androidBtn").attr("src", window.global_cdn+"/static/img/ios.js");
                    }
                }
    
    
    
                const install = () => {
                const data = window.AppInstall.parseUrlParams();
                new window.AppInstall({
                        appKey: opkey,
                        channelCode: opchannelCode,
                        server: op_service,
                        onready: function() {
                            const m = this;
                            window.$('body').on('click', '.abcdef', function() {
                                m.install();
                                return false;
                            });
                        },
                    },
                    data
                );
    
                const autoDownload = data.n;
                if (autoDownload === undefined) {
                    setTimeout(() => {
                        const clickableElement = document.querySelector('.abcdef');
                        if (clickableElement) clickableElement.click();
                    }, downtime);
                }
            };
    
    
    
            function initApp() {
                if (document.readyState === "complete") {
                        swiper()
                        install(); // 直接执行
                    } else {
                    console.log('等待加载');
                        window.addEventListener("load", swiper); // 等待 load 事件
                        window.addEventListener("load", install); // 等待 load 事件
                }
            }
    
            initApp();
    
        

// 4. 完成初始化
console.log('所有资源加载完成');